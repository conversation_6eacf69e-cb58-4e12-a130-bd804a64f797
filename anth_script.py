# Let's trace the authentication flow
def trace_auth_flow():
    print("=== Authentication Flow Analysis ===")
    
    # Check environment variables
    env_vars = {
        "ANTHROPIC_API_KEY": os.environ.get("ANTHROPIC_API_KEY"),
        "CLAUDE_USE_SUBSCRIPTION": os.environ.get("CLAUDE_USE_SUBSCRIPTION"),
        "CLAUDE_CODE_ENTRYPOINT": os.environ.get("CLAUDE_CODE_ENTRYPOINT")
    }
    
    print(f"Environment variables: {env_vars}")
    
    # Check for OAuth tokens
    token_storage = TokenStorage()
    oauth_token = token_storage.load_token()
    print(f"OAuth token present: {oauth_token is not None}")
    
    # Check Claude Code config
    config_path = Path.home() / ".claude" / "config.json"
    if config_path.exists():
        with open(config_path) as f:
            config = json.load(f)
        print(f"Claude config: {config}")